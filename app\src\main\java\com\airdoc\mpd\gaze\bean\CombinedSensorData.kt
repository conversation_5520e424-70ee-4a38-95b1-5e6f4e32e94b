package com.airdoc.mpd.gaze.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: CombinedSensorData
 * Author by lilin,Date on 2025/1/31 
 * PS: Not easy to write code, please indicate.
 * 综合传感器数据类（眼动+PPG+生理参数）
 */
@Parcelize
data class CombinedSensorData(
    // 眼动数据
    val gazeData: GazeTrackResult? = null,
    // PPG波形数据
    val ppgWaveData: PPGWaveData? = null,
    // 生理参数数据
    val physiologicalParams: PhysiologicalParams? = null,
    // 综合数据时间戳（毫秒）
    val timestamp: Long = System.currentTimeMillis(),
    // 数据同步标识（用于确保数据时间同步）
    val syncId: String = "",
    // 数据类型标识
    val dataType: String = "combined"
) : Parcelable {
    
    /**
     * 检查是否有眼动数据
     */
    fun hasGazeData(): Boolean {
        return gazeData != null && gazeData.valid
    }
    
    /**
     * 检查是否有PPG数据
     */
    fun hasPPGData(): Boolean {
        return ppgWaveData != null && ppgWaveData.isValid()
    }
    
    /**
     * 检查是否有生理参数数据
     */
    fun hasPhysiologicalData(): Boolean {
        return physiologicalParams != null && physiologicalParams.hasValidData()
    }
    
    /**
     * 获取数据完整性评分 0-100
     */
    fun getDataCompletenessScore(): Int {
        var score = 0
        if (hasGazeData()) score += 40
        if (hasPPGData()) score += 30
        if (hasPhysiologicalData()) score += 30
        return score
    }
    
    /**
     * 检查数据是否完整（包含所有三种数据）
     */
    fun isDataComplete(): Boolean {
        return hasGazeData() && hasPPGData() && hasPhysiologicalData()
    }
    
    /**
     * 获取数据摘要信息
     */
    fun getDataSummary(): String {
        val parts = mutableListOf<String>()
        if (hasGazeData()) parts.add("Gaze")
        if (hasPPGData()) parts.add("PPG")
        if (hasPhysiologicalData()) parts.add("Physio")
        return parts.joinToString("+")
    }
}
