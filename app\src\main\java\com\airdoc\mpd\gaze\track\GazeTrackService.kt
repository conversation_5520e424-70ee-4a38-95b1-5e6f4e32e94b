package com.airdoc.mpd.gaze.track

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.camera.core.ImageProxy
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus
import com.airdoc.mpd.MpdApplication
import com.airdoc.mpd.gaze.GazeTrackingManager
import com.airdoc.mpd.R
import com.airdoc.mpd.ServiceId
import com.airdoc.mpd.gaze.GazeConstants
import com.airdoc.mpd.gaze.MaskManager
import com.airdoc.mpd.gaze.bean.GazeTrackResult
import com.airdoc.mpd.device.DeviceManager
import com.airdoc.mpd.gaze.GazeError
import com.airdoc.mpd.gaze.camera.GTCameraManager
import com.airdoc.mpd.gaze.application.AppliedManager
import com.airdoc.mpd.gaze.bean.CalibrateCoordinate
import com.airdoc.mpd.gaze.bean.CalibrationResult
import com.airdoc.mpd.gaze.bean.GazeMessage
import com.airdoc.mpd.gaze.bean.PostureCalibrationResult
import com.airdoc.mpd.gaze.calibration.CalibrationActivity
import com.airdoc.mpd.gaze.camera.ICameraListener
import com.airdoc.mpd.gaze.enumeration.AppliedMode
import com.airdoc.mpd.gaze.enumeration.CalibrationMode
import com.airdoc.mpd.gaze.enumeration.ServiceMode
import com.airdoc.mpd.gaze.listener.IGazeAppliedListener
import com.airdoc.mpd.gaze.listener.IGazeTrackListener
import com.airdoc.mpd.gaze.upload.ReportManager
import com.airdoc.mpd.gaze.upload.UploadCloudHolder
import com.airdoc.mpd.utils.NetworkUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.jessyan.autosize.AutoSizeCompat
import me.jessyan.autosize.AutoSizeConfig
import android.app.Application
import com.airdoc.mpd.gaze.GazeTrackingManager.PREFIX
import java.net.InetAddress
import java.net.InetSocketAddress
import com.airdoc.mpd.utils.GTUtils
import java.io.File
import java.io.IOException

/**
 * FileName: GazeTrackService
 * Author by lilin,Date on 2024/7/23 14:08
 * PS: Not easy to write code, please indicate.
 * 用于执行视线追踪的服务
 */
open class GazeTrackService : Service(), LifecycleOwner, CoroutineScope by MainScope(),
    ICameraListener, IGazeTrackListener, IGazeAppliedListener {

    companion object{
        private val TAG = "GAZE_PROCESS_" + GazeTrackService::class.java.simpleName
    }

    private lateinit var lifecycleRegistry:LifecycleRegistry

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    private val mGson = Gson()

    private lateinit var mHandler: LifecycleHandler
    private lateinit var mServiceMessage: Messenger
    private var mClientMessage:Messenger? = null

    //屏幕宽高
    private var mScreenWidth = 0
    private var mScreenHeight = 0

    //WebSocket服务,用于对外发送眼动数据
    private var mGazeWebSocketService: GazeWebSocketService? = null

    //是否显示注视点
    private var isDisplayViewpoint = GazeTrackingManager.getDisplayViewpoint()

    //遮盖疗法已治疗时长，单位秒
    private var mTreatmentDuration = 0
    //遮盖疗法计划治疗时长，单位秒
    private var mPlannedDuration = 0
    //弱视眼位置 左left 右right
    private var mEyePosition = MaskManager.getAmblyopicEye()
    //记录遮盖疗法时长
    private var mRecordDurationTreatmentJob:Job? = null
    //治疗开始时间
    private var mCureStartTime = 0L

    override fun onCreate() {
        super.onCreate()
        Logger.d(TAG, msg = "onCreate - 独立进程启动")

        // 打印进程信息
        ProcessUtils.logCurrentProcessInfo(this)
        ProcessUtils.checkGazeServiceProcess(this)

        lifecycleRegistry = LifecycleRegistry(this)
        lifecycleRegistry.currentState = Lifecycle.State.CREATED

        // 独立进程中跳过AutoSize初始化，因为Service不需要UI适配
        // AutoSize主要用于Activity的屏幕适配，Service中通常不需要
        try {
            // 直接设置设计尺寸，不调用init方法
            AutoSizeConfig.getInstance()
                .setDesignWidthInDp(960)
                .setDesignHeightInDp(540)
            Logger.d(TAG, msg = "AutoSize配置设置成功")
        } catch (e: Exception) {
            Logger.w(TAG, msg = "AutoSize配置失败，跳过: ${e.message}")
            // Service不依赖AutoSize，可以继续正常运行
        }

        mHandler = object : LifecycleHandler(Looper.getMainLooper(),this){
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                parseMessage(msg)
            }
        }
        mServiceMessage = Messenger(mHandler)

        mScreenWidth = ScreenUtil.getScreenWidth(this)
        mScreenHeight = ScreenUtil.getScreenHeight(this)

        //启动前台服务
        try {
            startForegroundService()
            Logger.d(TAG, msg = "前台服务启动成功")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "前台服务启动失败: ${e.message}")
        }

        //启动WebSocket服务
        try {
            startWebSocketServer()
            Logger.d(TAG, msg = "WebSocket服务启动成功")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "WebSocket服务启动失败: ${e.message}")
        }

        initListener()
        //初始化观察者
        initObserver()

        // 移除自动启动眼动追踪流程，改为通过消息通信控制
        // 服务启动后只进行基础初始化，等待客户端发送消息来控制相机和追踪
        Logger.d(TAG, msg = "服务基础初始化完成，等待客户端消息控制相机和追踪")

        Logger.i(TAG, msg = "✓ onCreate - 独立进程初始化完成")
        Logger.d(TAG, msg = "服务已准备好接受客户端连接")
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        Logger.d(TAG, msg = "onStartCommand - 独立进程服务启动命令")
        Logger.d(TAG, msg = "  Intent: $intent")
        Logger.d(TAG, msg = "  Flags: $flags")
        Logger.d(TAG, msg = "  StartId: $startId")

        // 打印进程信息
        ProcessUtils.logCurrentProcessInfo(this)

        lifecycleRegistry.currentState = Lifecycle.State.STARTED
        lifecycleRegistry.currentState = Lifecycle.State.RESUMED

        Logger.d(TAG, msg = "onStartCommand - 独立进程服务启动完成")
        return START_REDELIVER_INTENT
    }

    override fun onBind(intent: Intent?): IBinder {
        Logger.d(TAG, msg = "onBind - 客户端绑定到独立进程服务")
        Logger.d(TAG, msg = "  Intent: $intent")
        Logger.d(TAG, msg = "  Binder: ${mServiceMessage.binder}")

        // 检查进程状态
        ProcessUtils.checkGazeServiceProcess(this)

        return mServiceMessage.binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Logger.d(TAG, msg = "onUnbind - 客户端解绑独立进程服务")
        Logger.d(TAG, msg = "  Intent: $intent")
        Logger.d(TAG, msg = "  ClientMessage: ${mClientMessage != null}")

        mClientMessage = null

        Logger.d(TAG, msg = "onUnbind - 解绑完成，返回true允许重新绑定")
        return true // 返回true允许onRebind被调用
    }

    override fun onRebind(intent: Intent?) {
        Logger.d(TAG, msg = "onRebind - 客户端重新绑定到独立进程服务")
        Logger.d(TAG, msg = "  Intent: $intent")
        super.onRebind(intent)
    }

    override fun onDestroy() {
        Logger.d(TAG, msg = "onDestroy - 独立进程服务销毁")
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED

        try {
            //关闭眼动应用
            AppliedManager.destroy()
            //关闭眼动服务
            TrackingManager.destroy()
            //关闭相机
            GTCameraManager.stopCamera(this)
            //关闭WebSocket服务
            stopWebSocketServer()
        } catch (e: Exception) {
            Logger.e(TAG, msg = "onDestroy异常: ${e.message}")
        }

        // 清理客户端连接
        mClientMessage = null

        super.onDestroy()
        Logger.d(TAG, msg = "onDestroy - 独立进程服务销毁完成")
    }

    private fun initListener(){
        GTCameraManager.setCameraListener(this)
        TrackingManager.setGazeTrackListener(this)
        TrackingManager.setLifecycleOwner(this)
        AppliedManager.setGazeAppliedListener(this)
    }

    /**
     * 初始化观察者
     */
    private fun initObserver(){
        LiveEventBus.get<Boolean>(MpdApplication.EVENT_APP_FOREGROUND_STATE).observe(this){
            Logger.d(TAG, msg = "EVENT_APP_FOREGROUND_STATE : $it")
            val maskTherapyState = DeviceManager.getMaskTherapyState()
            if (!it && maskTherapyState){
                WidgetManager.showTreatmentProgress(this,mTreatmentDuration,mPlannedDuration,mEyePosition)
            }else{
                WidgetManager.removeTreatmentProgress()
            }
        }
        LiveEventBus.get<Boolean>(GazeConstants.EVENT_SWITCH_DISPLAY_VIEWPOINT).observe(this){
            Logger.d(TAG, msg = "EVENT_SWITCH_DISPLAY_VIEWPOINT : $it")
            isDisplayViewpoint = it
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val channelId = "com.airdoc.mpd"
        val channelName = "GazeTrackerService"
        (getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager).createNotificationChannel(
            NotificationChannel(channelId,channelName, NotificationManager.IMPORTANCE_NONE)
                .apply {
                    lockscreenVisibility = Notification.VISIBILITY_SECRET
                }
        )
        startForeground(ServiceId.GAZE_TRACKER_SERVICE_ID, Notification.Builder(this,channelId).build())
    }


    /**
     * 拷贝模型到本地文件（从GazeTrackingManager移动过来）
     */
    private suspend fun copyModel2Dir(context: Context) {
        val weightDir = context.getDir(GazeConstants.MODEL_DIR_NAME, Context.MODE_PRIVATE)
        val assetManager = context.assets
        try {
            // 列出assets文件夹下的所有文件和子文件夹
            val assetsList = assetManager.list(GazeConstants.MODEL_DIR_NAME)
            if (!assetsList.isNullOrEmpty()) {
                for (assets in assetsList) {
                    if (assets.endsWith(GazeConstants.MODEL_FILE_EXTENSION)) {
                        withContext(Dispatchers.IO) {
                            // 如果文件以 .rknn 结尾，就添加到列表中
                            val weightFile = File(weightDir, assets)
                            GTUtils.copyAssets2Dir(context, weightFile, "${GazeConstants.MODEL_DIR_NAME}/$assets")
                        }
                    }
                }
            }

            // 复制新增的两个文件：face_landmarker.task 和 opencv.js
            withContext(Dispatchers.IO) {
                // 复制 face_landmarker.task 文件
                val faceLandmarkerFile = File(weightDir, "face_landmarker.task")
                val faceLandmarkerSuccess = GTUtils.copyAssets2Dir(context, faceLandmarkerFile, "${GazeConstants.MODEL_DIR_NAME}/face_landmarker.task")
                Logger.d(TAG, msg = "复制face_landmarker.task文件: $faceLandmarkerSuccess, 路径: ${faceLandmarkerFile.absolutePath}")

                // 复制 opencv.js 文件
                val opencvJsFile = File(weightDir, "opencv.js")
                val opencvJsSuccess = GTUtils.copyAssets2Dir(context, opencvJsFile, "${GazeConstants.MODEL_DIR_NAME}/opencv.js")
                Logger.d(TAG, msg = "复制opencv.js文件: $opencvJsSuccess, 路径: ${opencvJsFile.absolutePath}")
            }

            // 复制默认校准参数文件
            withContext(Dispatchers.IO) {
                copyDefaultCalibrationParams(context, weightDir)
            }
        } catch (e: IOException) {
            // 处理异常情况
            e.printStackTrace()
        }
    }

    /**
     * 复制默认校准参数文件
     */
    private fun copyDefaultCalibrationParams(context: Context, configDir: File) {
        try {
            val calibParamFile = File(configDir, "calib_param.txt")
            val success = GTUtils.copyAssets2Dir(context, calibParamFile, "${GazeConstants.MODEL_DIR_NAME}/calib_param.txt")
            Logger.d(TAG, msg = "复制默认校准参数文件: $success, 路径: ${calibParamFile.absolutePath}")

            if (success && calibParamFile.exists()) {
                Logger.i(TAG, msg = "默认校准参数文件复制成功，可以直接开始追踪")
            } else {
                Logger.e(TAG, msg = "默认校准参数文件复制失败")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "复制默认校准参数文件异常: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 处理消息，包括进程间消息、服务间消息
     */
    open fun parseMessage(msg: Message){
        Logger.d(TAG, msg = "parseMessage - 独立进程服务收到消息: ${msg.what}")
        Logger.d(TAG, msg = "  消息来源: ${msg.replyTo}")
        Logger.d(TAG, msg = "  消息数据: ${msg.data}")

        when(msg.what){
            GazeConstants.MSG_SERVICE_CONNECTED ->{
                Logger.d(TAG, msg = "处理MSG_SERVICE_CONNECTED - 客户端连接确认")
                mClientMessage = msg.replyTo
                Logger.d(TAG, msg = "客户端Messenger已保存: ${mClientMessage != null}")
            }
            GazeConstants.MSG_TURN_ON_CAMERA ->{
                Logger.d(TAG, msg = "处理MSG_TURN_ON_CAMERA - 开启相机")
                launch {
                    try {
                        // 确保模型和配置文件已拷贝
                        Logger.d(TAG, msg = "确保模型和配置文件已拷贝")
                        copyModel2Dir(this@GazeTrackService)

                        // 设置摄像头监听器
                        Logger.d(TAG, msg = "设置摄像头监听器")
                        GTCameraManager.setCameraListener(this@GazeTrackService)

                        // 启动摄像头
                        Logger.d(TAG, msg = "启动摄像头")
                        GTCameraManager.startCamera(this@GazeTrackService, this@GazeTrackService)

                        Logger.i(TAG, msg = "✓ 相机启动完成")
                    } catch (e: Exception) {
                        Logger.e(TAG, msg = "启动相机异常: ${e.message}")
                    }
                }
            }
            GazeConstants.MSG_TURN_OFF_CAMERA ->{
                Logger.d(TAG, msg = "处理MSG_TURN_OFF_CAMERA - 关闭相机")
                GTCameraManager.stopCamera(this)
            }
            GazeConstants.MSG_START_TRACK ->{
                Logger.d(TAG, msg = "处理MSG_START_TRACK - 开始眼动追踪")
                startGazeTrack()
            }
            GazeConstants.MSG_STOP_TRACK ->{
                Logger.d(TAG, msg = "处理MSG_STOP_TRACK - 停止眼动追踪")
                stopGazeTrack()
            }
            GazeConstants.MSG_START_VISUAL_CALIBRATION ->{
                startVisualCalibration()
            }
            GazeConstants.MSG_STOP_VISUAL_CALIBRATION ->{
                stopVisualCalibration()
            }
            GazeConstants.MSG_START_POSTURE_CALIBRATION ->{
                val isCorrection = msg.data.getBoolean(GazeConstants.KEY_IS_CORRECTION)
                startPostureCalibration(isCorrection)
            }
            GazeConstants.MSG_STOP_POSTURE_CALIBRATION ->{
                val isCorrection = msg.data.getBoolean(GazeConstants.KEY_IS_CORRECTION)
                stopPostureCalibration(isCorrection)
            }
            GazeConstants.MSG_VISUAL_READY ->{
                val isReady = msg.data.getBoolean(GazeConstants.KEY_VISUAL_READY)
                TrackingManager.setVisualReady(isReady)
            }
            GazeConstants.MSG_STOP_APPLIED ->{
                stopApplied()
            }
            GazeConstants.MSG_START_APPLIED_CURE ->{
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_PARAM)){
                    try {
                        val params = msg.data.getString(GazeConstants.KEY_REPORT_PARAM).orEmpty()
                        val type = object : TypeToken<HashMap<String, Any>>() {}.type
                        ReportManager.setReportParams(mGson.fromJson<HashMap<String, Any>>(params, type))
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_HEADER)){
                    try {
                        val headers = msg.data.getString(GazeConstants.KEY_REPORT_HEADER).orEmpty()
                        val type = object : TypeToken<HashMap<String, Any>>() {}.type
                        ReportManager.setReportHeaders(mGson.fromJson<HashMap<String, Any>>(headers, type))
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }
                if (msg.data.containsKey(GazeConstants.KEY_REPORT_URL)){
                    ReportManager.setReportUrl(msg.data.getString(GazeConstants.KEY_REPORT_URL).orEmpty())
                }
                if (msg.data.containsKey(GazeConstants.KEY_BASE_URL)){
                    ReportManager.setBaseUrl(msg.data.getString(GazeConstants.KEY_BASE_URL).orEmpty())
                }
                val plannedDuration = if (msg.data.containsKey(GazeConstants.KEY_PLANNED_DURATION)){
                    msg.data.getInt(GazeConstants.KEY_PLANNED_DURATION)
                }else{
                    mPlannedDuration
                }
                val treatmentDuration = if (msg.data.containsKey(GazeConstants.KEY_TREATMENT_DURATION)){
                    msg.data.getInt(GazeConstants.KEY_TREATMENT_DURATION)
                }else{
                    mTreatmentDuration
                }
                startAppliedCure(plannedDuration, treatmentDuration)
            }
            GazeConstants.MSG_STOP_APPLIED_CURE ->{
                stopAppliedCure()
            }
            GazeConstants.MSG_START_APPLIED_READING ->{
                startAppliedReading()
            }
            GazeConstants.MSG_STOP_APPLIED_READING ->{
                stopAppliedReading()
            }
            GazeConstants.MSG_START_APPLIED_STARE ->{
                val x = msg.data.getFloat(GazeConstants.KEY_X)
                val y = msg.data.getFloat(GazeConstants.KEY_Y)
                startAppliedStare(x,y)
            }
            GazeConstants.MSG_STOP_APPLIED_STARE ->{
                stopAppliedStare()
            }
            GazeConstants.MSG_START_APPLIED_FOLLOW ->{
                startAppliedFollow()
            }
            GazeConstants.MSG_STOP_APPLIED_FOLLOW ->{
                stopAppliedFollow()
            }
            GazeConstants.MSG_START_APPLIED_GLANCE ->{
                startAppliedGlance()
            }
            GazeConstants.MSG_STOP_APPLIED_GLANCE ->{
                stopAppliedGlance()
            }
            GazeConstants.MSG_GET_GAZE_TRAJECTORY ->{
                getGazeTrajectory()
            }
            GazeConstants.MSG_SET_GLANCE_POINT ->{
                val x = msg.data.getFloat(GazeConstants.KEY_X)
                val y = msg.data.getFloat(GazeConstants.KEY_Y)
                setGlancePoint(x,y)
            }
            GazeConstants.MSG_START_UPLOAD_CLOUD ->{
                startUploadCloud()
            }
        }
    }

    /**
     * 启动视线追踪
     */
    private fun startGazeTrack(){
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "开始启动视线追踪")

        // 设置TrackingManager的生命周期和监听器
        Logger.d(TAG, msg = "设置TrackingManager监听器")
        TrackingManager.setLifecycleOwner(this@GazeTrackService)
        TrackingManager.setGazeTrackListener(this@GazeTrackService)

        // 尝试加载校准参数
        val paramsLoaded = TrackingManager.updateParams()
        Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "校准参数加载结果: $paramsLoaded")

        TrackingManager.startTracking(this).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startGazeTrack isSuccess = $it")
            when(it){
                0 ->{ // 失败
                    if (!TrackingManager.checkCalibrationParam()){
                        Toast.makeText(this, "校准参数无效，请检查配置文件", Toast.LENGTH_LONG).show()
                        Logger.e(TAG, prefix = GazeTrackingManager.PREFIX, msg = "校准参数检查失败")
                    } else {
                        Toast.makeText(this, "启动追踪失败", Toast.LENGTH_LONG).show()
                        Logger.e(TAG, prefix = GazeTrackingManager.PREFIX, msg = "启动追踪失败，但校准参数正常")
                    }
                    // 发送失败状态给客户端
                    sendMessageToClient(Message.obtain().apply {
                        what = GazeConstants.MSG_GAZE_TRACKING_STATE
                        data.putBoolean(GazeConstants.KEY_STATE,false)
                    })
                }
                1 ->{ // 成功
                    Logger.i(TAG, prefix = GazeTrackingManager.PREFIX, msg = "视线追踪启动成功")
                    // 确保服务模式正确设置为TRACK
                    TrackingManager.onGazeServiceModeChange(ServiceMode.TRACK)
                    Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "显式设置服务模式为TRACK")

                    sendMessageToClient(Message.obtain().apply {
                        what = GazeConstants.MSG_GAZE_TRACKING_STATE
                        data.putBoolean(GazeConstants.KEY_STATE,true)
                    })
                }
            }
        }
    }

    /**
     * 停止视线追踪
     */
    private fun stopGazeTrack(){
        TrackingManager.stopTracking().also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopGazeTrack isSuccess = $it")
            WidgetManager.removeDotView()
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_GAZE_TRACKING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }



    /**
     * 启动视标校准
     */
    private fun startVisualCalibration(){
        TrackingManager.startVisualCalibration(this).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startCalibration isSuccess = $it")
        }
    }

    /**
     * 停止视标校准
     */
    private fun stopVisualCalibration(){
        TrackingManager.stopVisualCalibration().also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "stopVisualCalibration isSuccess = $it")
        }
    }

    /**
     * 开始姿势校准
     * @param  isCorrection true表示姿势矫正(视线追踪过程中的姿势校准)，false表示普通姿势校准
     */
    private fun startPostureCalibration(isCorrection:Boolean){
        TrackingManager.startPostureCalibration(this,isCorrection).also {
            Logger.d(TAG, prefix = GazeTrackingManager.PREFIX, msg = "startPostureCalibration isSuccess = $it, isCorrection = $isCorrection")
        }
    }

    /**
     * 中断姿势校准
     * @param  isCorrection true表示姿势矫正(视线追踪过程中的姿势校准)，false表示普通姿势校
     */
    private fun stopPostureCalibration(isCorrection:Boolean){
        TrackingManager.stopPostureCalibration().also {
            Logger.d(TAG, msg = "stopPostureCalibration isSuccess = $it, isCorrection = $isCorrection")
        }
    }

    /**
     * 上报校准数据
     */
    private fun startUploadCloud(){
        launch(Dispatchers.IO) {
            // 上报数据到云
            UploadCloudHolder.startUploadCloud()
        }
    }

    /**
     * 停止视线追踪应用
     */
    private fun stopApplied(){
        when(AppliedManager.getAppliedMode()){
            AppliedMode.CURE -> stopAppliedCure()
            AppliedMode.READING -> stopAppliedReading()
            AppliedMode.STARE -> stopAppliedStare()
            AppliedMode.FOLLOW -> stopAppliedFollow()
            AppliedMode.GLANCE -> stopAppliedGlance()
            else -> {
            }
        }
    }

    /**
     * 启动眼动应用-治疗
     * @param plannedDuration  计划训练时长，单位秒
     * @param treatmentDuration 已治疗训练时长，单位秒
     */
    private fun startAppliedCure(plannedDuration:Int,treatmentDuration:Int){
        mTreatmentDuration = maxOf(0, treatmentDuration)
        mPlannedDuration = if (plannedDuration <= mTreatmentDuration) mTreatmentDuration + 5400 else plannedDuration
        Logger.d(TAG, msg = "startAppliedCure plannedDuration = $mPlannedDuration, treatmentDuration = $mTreatmentDuration")
        AppliedManager.startAppliedCure().also {
            Logger.d(TAG, msg = "startAppliedCure isSuccess = $it")
            if (it == 1){
                startTreatmentTimer()
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_CURE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-治疗
     */
    private fun stopAppliedCure(){
        Logger.d(TAG, msg = "stopAppliedCure")
        AppliedManager.stopAppliedCure().also {
            Logger.d(TAG, msg = "stopAppliedCure isSuccess = $it")
            if (it == 1){
                WidgetManager.removeTreatmentProgress()
                stopTreatmentTimer()
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_CURE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-阅读
     */
    private fun startAppliedReading(){
        Logger.d(TAG, msg = "startAppliedReading")
        AppliedManager.startAppliedReading().also {
            Logger.d(TAG, msg = "startAppliedReading isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_READING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-阅读
     */
    private fun stopAppliedReading(){
        Logger.d(TAG, msg = "stopAppliedReading")
        AppliedManager.stopAppliedReading().also {
            Logger.d(TAG, msg = "stopAppliedReading isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_READING_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-注视
     * @param x 注视点x坐标 [0~1]
     * @param y 注视点y坐标 [0~1]
     */
    private fun startAppliedStare(x:Float,y:Float){
        Logger.d(TAG, msg = "startAppliedStare x = $x, y = $y")
        AppliedManager.startAppliedStare(x,y).also {
            Logger.d(TAG, msg = "startAppliedStare isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_STARE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-注视
     */
    private fun stopAppliedStare(){
        Logger.d(TAG, msg = "stopAppliedStare")
        AppliedManager.stopAppliedStare().also {
            Logger.d(TAG, msg = "stopAppliedStare isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_STARE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-追随
     */
    private fun startAppliedFollow(){
        Logger.d(TAG, msg = "startAppliedFollow")
        AppliedManager.startAppliedFollow().also {
            Logger.d(TAG, msg = "startAppliedFollow isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_FOLLOW_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-追随
     */
    private fun stopAppliedFollow(){
        Logger.d(TAG, msg = "stopAppliedFollow")
        AppliedManager.stopAppliedFollow().also {
            Logger.d(TAG, msg = "stopAppliedFollow isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_FOLLOW_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 启动眼动应用-眼动能力检查-扫视
     */
    private fun startAppliedGlance(){
        Logger.d(TAG, msg = "startAppliedGlance")
        AppliedManager.startAppliedGlance().also {
            Logger.d(TAG, msg = "startAppliedGlance isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_GLANCE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,true)
                })
            }
        }
    }

    /**
     * 停止眼动应用-眼动能力检查-扫视
     */
    private fun stopAppliedGlance(){
        Logger.d(TAG, msg = "stopAppliedGlance")
        AppliedManager.stopAppliedGlance().also {
            Logger.d(TAG, msg = "stopAppliedGlance isSuccess = $it")
            if (it == 1){
                sendMessageToClient(Message.obtain().apply {
                    what = GazeConstants.MSG_APPLIED_GLANCE_STATE
                    data.putBoolean(GazeConstants.KEY_STATE,false)
                })
            }
        }
    }

    /**
     * 获取眼动轨迹数据结果
     */
    private fun getGazeTrajectory(){
        val gazeTrajectory = AppliedManager.getGazeTrajectory()
        Logger.d(TAG, msg = "getGazeTrajectory = $gazeTrajectory")
        sendMessageToClient(Message.obtain().apply {
            what = GazeConstants.MSG_GAZE_TRAJECTORY_RESULT
            data.putString(GazeConstants.KEY_GAZE_TRAJECTORY,gazeTrajectory)
        })
    }

    /**
     * 设置扫视的目标点，[AppliedMode.GLANCE]模式下使用
     * @param x 目标点 - x [0~1]
     * @param y 目标点 - y [0~1]
     */
    private fun setGlancePoint(x: Float, y: Float){
        AppliedManager.setGlancePoint(x,y)
    }

    /**
     * 发送消息到客户端
     */
    private fun sendMessageToClient(msg: Message){
        Logger.d(TAG, msg = "准备发送消息到客户端: ${msg.what}")
        Logger.d(TAG, msg = "  客户端Messenger: ${mClientMessage != null}")
        Logger.d(TAG, msg = "  消息数据: ${msg.data}")

        try {
            if (mClientMessage != null) {
                mClientMessage?.send(msg)
                Logger.d(TAG, msg = "发送消息到客户端成功: ${msg.what}")
            } else {
                Logger.w(TAG, msg = "客户端Messenger为null，无法发送消息: ${msg.what}")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "发送消息到客户端失败: ${e.message}")
            Logger.e(TAG, msg = "  消息类型: ${msg.what}")
            Logger.e(TAG, msg = "  异常堆栈: ${e.stackTrace.contentToString()}")
        }
    }

    /**
     * 开启WebSocketServer
     */
    private fun startWebSocketServer(){
        Log.d(TAG,"startWebSocketServer")
        lifecycleScope.launch(Dispatchers.Default) {
            val localHost = withContext(Dispatchers.IO) {
                InetAddress.getLocalHost()
            }
            val inetSocketAddress = InetSocketAddress(localHost, 9200)
            mGazeWebSocketService = GazeWebSocketService(inetSocketAddress).apply {
                isReuseAddr = true
            }
            mGazeWebSocketService?.start()
            Logger.d(TAG, msg = "startWebSocketServer hostname = $localHost -- port = 9200")
        }

    }

    /**
     * 停止WebSocketServer
     */
    private fun stopWebSocketServer(){
        mGazeWebSocketService?.stop()
        mGazeWebSocketService = null
    }

    /**
     * 开始治疗计时
     */
    private fun startTreatmentTimer(){
        //60秒记录一次
        val interval = 60
        stopTreatmentTimer()
        val maskTherapyState = DeviceManager.getMaskTherapyState()
        if (maskTherapyState){
            mCureStartTime = System.currentTimeMillis()
            mRecordDurationTreatmentJob = lifecycleScope.launch(Dispatchers.Default) {
                while(true){
                    delay(interval.toLong() * 1000)
                    withContext(Dispatchers.Main){
                        mTreatmentDuration += 60
                        WidgetManager.updateTreatmentProgress(mTreatmentDuration,mPlannedDuration)
                        //发送治疗时长给客户端
                        sendMessageToClient(Message.obtain().apply {
                            what = GazeConstants.MSG_UPDATE_TREATMENT_DURATION
                            data.putInt(GazeConstants.KEY_TREATMENT_DURATION,mTreatmentDuration)
                        })
                        if (mTreatmentDuration >= mPlannedDuration){
                            //治疗结束，关闭服务
                            stopAppliedCure()
                            stopGazeTrack()
                        }
                    }
                }
            }.apply {
                invokeOnCompletion {
                    Logger.d(TAG, msg = "invokeOnCompletion")
                    val duration = (System.currentTimeMillis() - mCureStartTime) / 1000
                    onStopTreatmentTimer(duration.toInt())
                }
            }
        }
    }

    /**
     * 停止治疗计时
     */
    private fun stopTreatmentTimer(){
        mRecordDurationTreatmentJob?.cancel()
        mRecordDurationTreatmentJob = null
    }

    open fun onStopTreatmentTimer(duration: Int){
        ReportManager.reportCureResult(duration)
    }

    //---------------Begin: for ICameraListener ----------
    override fun onCameraStatusChange(isOn: Boolean) {
//        if (isOn){
//            TrackingManager.startImageAnalysis(this)
//        }else{
//            TrackingManager.stopImageAnalysis()
//        }
    }

    override fun onAnalyze(image: ImageProxy) {
        TrackingManager.sendImageProxy(image)
    }
    //---------------End: for ICameraListener ----------

    // WebSocket数据发送节流相关变量
    private var lastWebSocketSendTime = 0L
    private val webSocketSendInterval = 500L // 约30fps，可以调整为50L(20fps)或100L(10fps)来降低频率

    //---------------Begin: for IGazeTrackListener ----------
    override fun onGazeTracking(gazeTrackResult: GazeTrackResult) {
        val appliedMode = AppliedManager.getAppliedMode()
        if (gazeTrackResult.skew && appliedMode == AppliedMode.CURE){
            if (TrackingManager.isSkewing.get()) return
            //姿势偏移
            val intent = CalibrationActivity.createIntent(this@GazeTrackService, CalibrationMode.POSTURE,true)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
        }else{
            if (isDisplayViewpoint){
                WidgetManager.showDotView(this@GazeTrackService,gazeTrackResult,mScreenWidth,mScreenHeight)
            }else{
                WidgetManager.removeDotView()
            }

            // 添加WebSocket发送频率限制，避免网页卡顿
//            val currentTime = System.currentTimeMillis()
//            if (currentTime - lastWebSocketSendTime >= webSocketSendInterval) {
//                lastWebSocketSendTime = currentTime

                //发布眼动数据到WebSocket
                val resultJson = mGson.toJson(gazeTrackResult)
                Logger.d(TAG, prefix = PREFIX, msg = "WebSocket广播眼动数据: $resultJson")
                mGazeWebSocketService?.broadcast(resultJson)
//            }

            //发布眼动数据到应用（这个不需要节流，应用内部处理需要全部数据）
            AppliedManager.analyseTrackResult(gazeTrackResult)
        }
    }

    override fun onPostureCalibration(result: PostureCalibrationResult) {
        val gazeMessage = GazeMessage<PostureCalibrationResult>().apply {
            action = GazeMessage.ACTION_POSTURE_CALIBRATION_RESULT
            data = result
        }
        mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
    }

    override fun onCalibrating(result: CalibrationResult) {
        val gazeMessage = GazeMessage<CalibrationResult>().apply {
            action = GazeMessage.ACTION_CALIBRATION_RESULT
            data = result
        }
        mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
    }

    override fun onCalibrateCoordinate(calibrateCoordinate: CalibrateCoordinate) {
        lifecycleScope.launch {
            val gazeMessage = GazeMessage<CalibrateCoordinate>().apply {
                action = GazeMessage.ACTION_CALIBRATE_COORDINATE
                data = calibrateCoordinate
            }
            mGazeWebSocketService?.broadcast(mGson.toJson(gazeMessage))
        }
    }

    override fun onError(error: GazeError) {
        lifecycleScope.launch {
            handleError(error)
        }
    }
    //---------------End: for IGazeTrackListener ----------

    //---------------Begin: for IGazeAppliedListener ----------

    override fun onSaccadePointComplete() {
        Logger.d(TAG, msg = "onSaccadePointComplete")
        sendMessageToClient(Message.obtain().apply {
            what = GazeConstants.MSG_SACCADE_POINT_COMPLETE
        })
    }

    //---------------End: for IGazeAppliedListener ----------

    private fun handleError(error: GazeError){
        Logger.e(TAG, msg = "handleError code = ${error.code}, msg = ${error.msg}")
        if (!TextUtils.isEmpty(error.msg)){
            Toast.makeText(this@GazeTrackService,error.msg,Toast.LENGTH_SHORT).show()
        }
    }

}